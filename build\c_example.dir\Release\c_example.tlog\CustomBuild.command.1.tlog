^C:\USERS\<USER>\DESKTOP\驱动\DLLTEST\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/驱动/dlltest -BC:/Users/<USER>/Desktop/驱动/dlltest/build --check-stamp-file C:/Users/<USER>/Desktop/驱动/dlltest/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
