#include "../include/wnbios_dll.h"
#include "drv.h"
#include <mutex>
#include <memory>
#include <vector>
#include <string>
#include <thread>
#include <unordered_map>
#include <tlhelp32.h>

// 全局变量
static std::unique_ptr<eneio_lib> g_driver = nullptr;
static std::mutex g_driver_mutex;
static thread_local WnbiosErrorInfo g_last_error = {};
static WnbiosLogCallback g_log_callback = nullptr;
static WnbiosLogLevel g_log_level = WNBIOS_LOG_INFO;
static bool g_debug_mode = false;

// 内部辅助函数
static void SetLastError(int error_code, const char* message, const char* function_name) {
    g_last_error.error_code = error_code;
    strncpy_s(g_last_error.error_message, message, sizeof(g_last_error.error_message) - 1);
    strncpy_s(g_last_error.function_name, function_name, sizeof(g_last_error.function_name) - 1);
    g_last_error.thread_id = GetCurrentThreadId();
}

static void LogMessage(WnbiosLogLevel level, const char* format, ...) {
    if (level > g_log_level || !g_log_callback) return;
    
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf_s(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    g_log_callback(level, buffer);
}

// ================================
// 驱动管理API实现
// ================================

WNBIOS_API int WNBIOS_CALL wnbios_init_driver(void) {
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    try {
        if (g_driver != nullptr) {
            LogMessage(WNBIOS_LOG_INFO, "Driver already initialized");
            return WNBIOS_SUCCESS;
        }
        
        LogMessage(WNBIOS_LOG_INFO, "Initializing wnbios driver...");
        g_driver = std::make_unique<eneio_lib>();
        
        LogMessage(WNBIOS_LOG_INFO, "Driver initialized successfully");
        return WNBIOS_SUCCESS;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_INIT, e.what(), __FUNCTION__);
        LogMessage(WNBIOS_LOG_ERROR, "Failed to initialize driver: %s", e.what());
        return WNBIOS_ERROR_INIT;
    } catch (...) {
        SetLastError(WNBIOS_ERROR_INIT, "Unknown error during driver initialization", __FUNCTION__);
        LogMessage(WNBIOS_LOG_ERROR, "Unknown error during driver initialization");
        return WNBIOS_ERROR_INIT;
    }
}

WNBIOS_API int WNBIOS_CALL wnbios_cleanup_driver(void) {
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    try {
        if (g_driver == nullptr) {
            LogMessage(WNBIOS_LOG_INFO, "Driver not initialized");
            return WNBIOS_SUCCESS;
        }
        
        LogMessage(WNBIOS_LOG_INFO, "Cleaning up driver...");
        g_driver.reset();
        
        LogMessage(WNBIOS_LOG_INFO, "Driver cleaned up successfully");
        return WNBIOS_SUCCESS;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_DRIVER, e.what(), __FUNCTION__);
        LogMessage(WNBIOS_LOG_ERROR, "Failed to cleanup driver: %s", e.what());
        return WNBIOS_ERROR_DRIVER;
    } catch (...) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Unknown error during driver cleanup", __FUNCTION__);
        LogMessage(WNBIOS_LOG_ERROR, "Unknown error during driver cleanup");
        return WNBIOS_ERROR_DRIVER;
    }
}

WNBIOS_API int WNBIOS_CALL wnbios_is_driver_loaded(void) {
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    return (g_driver != nullptr) ? 1 : 0;
}

// ================================
// 进程操作API实现
// ================================



WNBIOS_API int WNBIOS_CALL wnbios_find_process_by_name(const char* name, WnbiosProcessInfo* process) {
    if (!name || !process) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid arguments", __FUNCTION__);
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return WNBIOS_ERROR_DRIVER;
    }
    
    try {
        uintptr_t base_addr = g_driver->get_process_base(name);
        if (base_addr == 0) {
            SetLastError(WNBIOS_ERROR_NOT_FOUND, "Process not found", __FUNCTION__);
            return WNBIOS_ERROR_NOT_FOUND;
        }
        
        // 获取进程ID
        DWORD pid = g_driver->get_process_id(name);
        
        strncpy_s(process->name, name, sizeof(process->name) - 1);
        process->pid = pid;
        process->base_address = base_addr;
        process->cr3 = 0; // 将在需要时获取
        
        LogMessage(WNBIOS_LOG_INFO, "Found process %s (PID: %d, Base: 0x%llx)", name, pid, base_addr);
        return WNBIOS_SUCCESS;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_PROCESS, e.what(), __FUNCTION__);
        return WNBIOS_ERROR_PROCESS;
    }
}

WNBIOS_API uintptr_t WNBIOS_CALL wnbios_get_process_base(const char* process_name) {
    if (!process_name) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid process name", __FUNCTION__);
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return 0;
    }
    
    try {
        uintptr_t base_addr = g_driver->get_process_base(process_name);
        if (base_addr == 0) {
            SetLastError(WNBIOS_ERROR_NOT_FOUND, "Process not found", __FUNCTION__);
        }
        return base_addr;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_PROCESS, e.what(), __FUNCTION__);
        return 0;
    }
}

static std::string g_target_process;

WNBIOS_API int WNBIOS_CALL wnbios_set_target_process(const char* process_name) {
    if (!process_name) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid process name", __FUNCTION__);
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return WNBIOS_ERROR_DRIVER;
    }
    
    try {
        // 验证进程是否存在
        uintptr_t base_addr = g_driver->get_process_base(process_name);
        if (base_addr == 0) {
            SetLastError(WNBIOS_ERROR_NOT_FOUND, "Process not found", __FUNCTION__);
            return WNBIOS_ERROR_NOT_FOUND;
        }
        
        g_target_process = process_name;
        LogMessage(WNBIOS_LOG_INFO, "Target process set to: %s", process_name);
        return WNBIOS_SUCCESS;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_PROCESS, e.what(), __FUNCTION__);
        return WNBIOS_ERROR_PROCESS;
    }
}

// ================================
// 内存操作API实现
// ================================

WNBIOS_API int WNBIOS_CALL wnbios_read_memory(uintptr_t address, void* buffer, size_t size) {
    if (!buffer || size == 0) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid arguments", __FUNCTION__);
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return WNBIOS_ERROR_DRIVER;
    }
    
    try {
        bool success = g_driver->read_virtual_memory(address, buffer, size);
        if (!success) {
            SetLastError(WNBIOS_ERROR_MEMORY, "Failed to read memory", __FUNCTION__);
            return WNBIOS_ERROR_MEMORY;
        }
        
        LogMessage(WNBIOS_LOG_DEBUG, "Read %zu bytes from 0x%llx", size, address);
        return (int)size;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_MEMORY, e.what(), __FUNCTION__);
        return WNBIOS_ERROR_MEMORY;
    }
}

WNBIOS_API int WNBIOS_CALL wnbios_write_memory(uintptr_t address, const void* data, size_t size) {
    if (!data || size == 0) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid arguments", __FUNCTION__);
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return WNBIOS_ERROR_DRIVER;
    }
    
    try {
        bool success = g_driver->write_virtual_memory(address, const_cast<void*>(data), size);
        if (!success) {
            SetLastError(WNBIOS_ERROR_MEMORY, "Failed to write memory", __FUNCTION__);
            return WNBIOS_ERROR_MEMORY;
        }
        
        LogMessage(WNBIOS_LOG_DEBUG, "Wrote %zu bytes to 0x%llx", size, address);
        return (int)size;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_MEMORY, e.what(), __FUNCTION__);
        return WNBIOS_ERROR_MEMORY;
    }
}

WNBIOS_API int WNBIOS_CALL wnbios_read_memory_batch(const uintptr_t* addresses, void** buffers, const size_t* sizes, int count) {
    if (!addresses || !buffers || !sizes || count <= 0) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid arguments", __FUNCTION__);
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return WNBIOS_ERROR_DRIVER;
    }
    
    try {
        int success_count = 0;
        for (int i = 0; i < count; i++) {
            if (buffers[i] && sizes[i] > 0) {
                bool success = g_driver->read_virtual_memory(addresses[i], buffers[i], sizes[i]);
                if (success) {
                    success_count++;
                }
            }
        }
        
        LogMessage(WNBIOS_LOG_DEBUG, "Batch read: %d/%d successful", success_count, count);
        return (success_count == count) ? WNBIOS_SUCCESS : WNBIOS_ERROR_MEMORY;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_MEMORY, e.what(), __FUNCTION__);
        return WNBIOS_ERROR_MEMORY;
    }
}

// ================================
// 模块操作API实现
// ================================

WNBIOS_API int WNBIOS_CALL wnbios_enum_modules(const char* process_name, WnbiosModuleInfo* modules, int* count) {
    if (!process_name || !modules || !count) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid arguments", __FUNCTION__);
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return WNBIOS_ERROR_DRIVER;
    }
    
    try {
        // 通过驱动获取PEB地址并遍历模块
        uintptr_t peb_addr = g_driver->get_process_peb(process_name);
        if (peb_addr == 0) {
            SetLastError(WNBIOS_ERROR_NOT_FOUND, "Failed to get process PEB", __FUNCTION__);
            return WNBIOS_ERROR_NOT_FOUND;
        }
        
        int max_count = *count;
        int module_count = 0;
        
        // 通过驱动读取PEB结构
        // PEB + 0x18 = PEB_LDR_DATA
        uintptr_t ldr_data_ptr = 0;
        if (!g_driver->read_virtual_memory(peb_addr + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
            SetLastError(WNBIOS_ERROR_MODULE, "Failed to read LDR data pointer", __FUNCTION__);
            return WNBIOS_ERROR_MODULE;
        }
        
        // PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
        uintptr_t module_list = 0;
        if (!g_driver->read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
            SetLastError(WNBIOS_ERROR_MODULE, "Failed to read module list", __FUNCTION__);
            return WNBIOS_ERROR_MODULE;
        }
        
        uintptr_t current_entry = module_list;
        
        // 遍历模块链表
        for (int i = 0; i < 100 && module_count < max_count && current_entry != 0; i++) {
            // 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
            uintptr_t ldr_entry = current_entry - 0x10;
            
            // DllBase 在偏移 0x30
            uintptr_t dll_base = 0;
            if (!g_driver->read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
                break;
            }
            
            // SizeOfImage 在偏移 0x40
            uint32_t size_of_image = 0;
            g_driver->read_virtual_memory(ldr_entry + 0x40, &size_of_image, sizeof(size_of_image));
            
            // BaseDllName 在偏移 0x58 (UNICODE_STRING)
            struct {
                uint16_t Length;
                uint16_t MaximumLength;
                uintptr_t Buffer;
            } unicode_string;
            
            wchar_t module_name[256] = {0};
            if (g_driver->read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
                if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
                    size_t read_size = min(unicode_string.Length, sizeof(module_name) - 2);
                    g_driver->read_virtual_memory(unicode_string.Buffer, module_name, read_size);
                }
            }
            
            // 填充模块信息
            if (dll_base != 0 && wcslen(module_name) > 0) {
                wcsncpy_s(modules[module_count].name, module_name, sizeof(modules[module_count].name) / sizeof(wchar_t) - 1);
                modules[module_count].base_address = dll_base;
                modules[module_count].size = size_of_image;
                modules[module_count].entry_point = 0; // 可以从PE头读取
                module_count++;
            }
            
            // 获取下一个条目
            if (!g_driver->read_virtual_memory(current_entry, &current_entry, sizeof(current_entry))) {
                break;
            }
            
            // 检查是否回到起始点
            if (current_entry == module_list && i > 0) {
                break;
            }
        }
        
        *count = module_count;
        LogMessage(WNBIOS_LOG_INFO, "Enumerated %d modules for process %s via driver", module_count, process_name);
        return WNBIOS_SUCCESS;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_MODULE, e.what(), __FUNCTION__);
        return WNBIOS_ERROR_MODULE;
    }
}

WNBIOS_API uintptr_t WNBIOS_CALL wnbios_find_module_base(const char* process_name, const wchar_t* module_name) {
    if (!process_name || !module_name) {
        SetLastError(WNBIOS_ERROR_INVALID_ARG, "Invalid arguments", __FUNCTION__);
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(g_driver_mutex);
    
    if (!g_driver) {
        SetLastError(WNBIOS_ERROR_DRIVER, "Driver not initialized", __FUNCTION__);
        return 0;
    }
    
    try {
        uintptr_t base_addr = g_driver->find_module_base(process_name, module_name);
        if (base_addr == 0) {
            SetLastError(WNBIOS_ERROR_NOT_FOUND, "Module not found", __FUNCTION__);
        }
        return base_addr;
        
    } catch (const std::exception& e) {
        SetLastError(WNBIOS_ERROR_MODULE, e.what(), __FUNCTION__);
        return 0;
    }
}

// ================================
// 错误处理API实现
// ================================

WNBIOS_API int WNBIOS_CALL wnbios_get_last_error(WnbiosErrorInfo* error_info) {
    if (!error_info) {
        return WNBIOS_ERROR_INVALID_ARG;
    }
    
    *error_info = g_last_error;
    return WNBIOS_SUCCESS;
}

WNBIOS_API void WNBIOS_CALL wnbios_clear_error(void) {
    memset(&g_last_error, 0, sizeof(g_last_error));
}

WNBIOS_API const char* WNBIOS_CALL wnbios_get_error_string(int error_code) {
    switch (error_code) {
        case WNBIOS_SUCCESS: return "Success";
        case WNBIOS_ERROR_INIT: return "Initialization error";
        case WNBIOS_ERROR_DRIVER: return "Driver error";
        case WNBIOS_ERROR_PROCESS: return "Process error";
        case WNBIOS_ERROR_MEMORY: return "Memory error";
        case WNBIOS_ERROR_MODULE: return "Module error";
        case WNBIOS_ERROR_INVALID_ARG: return "Invalid argument";
        case WNBIOS_ERROR_BUFFER_SIZE: return "Buffer size error";
        case WNBIOS_ERROR_ACCESS: return "Access denied";
        case WNBIOS_ERROR_NOT_FOUND: return "Not found";
        case WNBIOS_ERROR_TIMEOUT: return "Timeout";
        default: return "Unknown error";
    }
}

// ================================
// 日志和调试API实现
// ================================

WNBIOS_API void WNBIOS_CALL wnbios_set_log_callback(WnbiosLogCallback callback) {
    g_log_callback = callback;
}

WNBIOS_API void WNBIOS_CALL wnbios_set_log_level(WnbiosLogLevel level) {
    g_log_level = level;
}

WNBIOS_API void WNBIOS_CALL wnbios_set_debug_mode(int enable) {
    g_debug_mode = (enable != 0);
    if (g_debug_mode) {
        g_log_level = WNBIOS_LOG_DEBUG;
    }
}

// ================================
// 实用工具API实现
// ================================

WNBIOS_API void WNBIOS_CALL wnbios_get_version(int* major, int* minor, int* patch) {
    if (major) *major = 1;
    if (minor) *minor = 0;
    if (patch) *patch = 0;
}

WNBIOS_API const char* WNBIOS_CALL wnbios_get_supported_versions(void) {
    return "Windows 10/11 (Build 17763+)";
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            LogMessage(WNBIOS_LOG_INFO, "wnbios_dll loaded");
            break;
        case DLL_PROCESS_DETACH:
            // 自动清理
            wnbios_cleanup_driver();
            LogMessage(WNBIOS_LOG_INFO, "wnbios_dll unloaded");
            break;
        case DLL_THREAD_ATTACH:
        case DLL_THREAD_DETACH:
            break;
    }
    return TRUE;
}