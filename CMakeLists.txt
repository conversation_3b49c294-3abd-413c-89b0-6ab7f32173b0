cmake_minimum_required(VERSION 3.16)
project(wnbios_dll_standalone)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(include)
include_directories(src)

# 源文件 - 所有文件都在独立文件夹中
set(DLL_SOURCES
    src/wnbios_dll.cpp
    src/drv.cpp
)

# 头文件
set(DLL_HEADERS
    include/wnbios_dll.h
    src/drv.h
)

# 创建DLL
add_library(wnbios_dll SHARED ${DLL_SOURCES} ${DLL_HEADERS})

# 设置DLL属性
set_target_properties(wnbios_dll PROPERTIES
    OUTPUT_NAME "wnbios_dll"
    PREFIX ""
    SUFFIX ".dll"
)

# 链接库
target_link_libraries(wnbios_dll
    ntdll
    kernel32
    user32
    advapi32
)

# 定义预处理器宏
target_compile_definitions(wnbios_dll PRIVATE
    WNBIOS_DLL_EXPORTS
    _CRT_SECURE_NO_WARNINGS
    _MBCS
)

# 设置模块定义文件
set_target_properties(wnbios_dll PROPERTIES
    LINK_FLAGS "/DEF:${CMAKE_CURRENT_SOURCE_DIR}/src/wnbios_dll.def"
)

# 创建C示例程序
add_executable(c_example examples/c_example.c)
target_link_libraries(c_example wnbios_dll)
target_include_directories(c_example PRIVATE include)

# 复制头文件到输出目录
configure_file(include/wnbios_dll.h ${CMAKE_BINARY_DIR}/include/wnbios_dll.h COPYONLY)

# 安装规则
install(TARGETS wnbios_dll
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES include/wnbios_dll.h
    DESTINATION include
)

install(TARGETS c_example
    RUNTIME DESTINATION examples
)