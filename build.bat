@echo off
echo ========================================
echo    wnbios_dll 独立编译脚本 (VS2022)
echo ========================================
echo.

REM 检查是否在VS开发者命令提示符中
if "%VSINSTALLDIR%"=="" (
    echo 错误: 请在 Visual Studio 2022 开发者命令提示符中运行此脚本
    echo.
    echo 启动方法:
    echo 1. 打开开始菜单
    echo 2. 搜索 "Developer Command Prompt for VS 2022"
    echo 3. 以管理员身份运行
    echo 4. 导航到此目录并运行 build.bat
    echo.
    pause
    exit /b 1
)

echo 检测到 Visual Studio 环境: %VSINSTALLDIR%
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 警告: 建议以管理员权限运行以确保驱动正常工作
    echo.
)

REM 检查 CMake
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 CMake
    echo 请安装 CMake 或确保它在 PATH 中
    pause
    exit /b 1
)

echo CMake 版本:
cmake --version | findstr "cmake version"
echo.

REM 清理旧的构建文件
if exist build (
    echo 清理旧的构建文件...
    rmdir /s /q build
)

REM 创建构建目录
mkdir build
cd build

echo 正在生成 Visual Studio 2022 项目文件...
cmake .. -G "Visual Studio 17 2022" -A x64

if %errorlevel% neq 0 (
    echo CMake 配置失败
    cd ..
    pause
    exit /b 1
)

echo.
echo 正在编译项目 (Release 配置)...
cmake --build . --config Release --parallel

if %errorlevel% neq 0 (
    echo 编译失败
    cd ..
    pause
    exit /b 1
)

echo.
echo 编译成功！正在整理输出文件...

REM 复制必要文件
cd ..
if not exist dist mkdir dist
if not exist dist\include mkdir dist\include
if not exist dist\lib mkdir dist\lib
if not exist dist\bin mkdir dist\bin
if not exist dist\examples mkdir dist\examples

copy /Y build\bin\Release\wnbios_dll.dll dist\bin\ >nul
copy /Y build\lib\Release\wnbios_dll.lib dist\lib\ >nul
copy /Y include\wnbios_dll.h dist\include\ >nul
copy /Y build\bin\Release\c_example.exe dist\examples\ >nul
copy /Y examples\c_example.c dist\examples\ >nul

echo.
echo ========================================
echo           编译完成！
echo ========================================
echo.
echo 输出文件位于 dist 目录：
echo   ? dist\bin\
echo      └── wnbios_dll.dll     (动态链接库)
echo   ? dist\lib\
echo      └── wnbios_dll.lib     (导入库)
echo   ? dist\include\
echo      └── wnbios_dll.h       (C API 头文件)
echo   ? dist\examples\
echo      ├── c_example.exe      (C 示例程序)
echo      └── c_example.c        (C 源码)
echo.
echo ? 快速测试:
echo    cd dist\examples
echo    c_example.exe
echo.
echo ??  注意: 运行示例程序需要管理员权限
echo.
pause