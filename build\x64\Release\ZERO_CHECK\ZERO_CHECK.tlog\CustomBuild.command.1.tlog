^C:\USERS\<USER>\DESKTOP\驱动\DLLTEST\BUILD\CMAKEFILES\A89ADDDC0057655BA287C928523211AC\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/驱动/dlltest -BC:/Users/<USER>/Desktop/驱动/dlltest/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/驱动/dlltest/build/wnbios_dll_standalone.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
