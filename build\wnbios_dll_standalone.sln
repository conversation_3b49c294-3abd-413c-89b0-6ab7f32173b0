﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{C3829F0A-290C-39A6-83D8-3079E5B42D96}"
	ProjectSection(ProjectDependencies) = postProject
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE} = {8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC} = {FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7} = {BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{AF8F6CC8-25AD-3FDE-897F-6746090D4499}"
	ProjectSection(ProjectDependencies) = postProject
		{C3829F0A-290C-39A6-83D8-3079E5B42D96} = {C3829F0A-290C-39A6-83D8-3079E5B42D96}
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE} = {8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "c_example", "c_example.vcxproj", "{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}"
	ProjectSection(ProjectDependencies) = postProject
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE} = {8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7} = {BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "wnbios_dll", "wnbios_dll.vcxproj", "{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}"
	ProjectSection(ProjectDependencies) = postProject
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE} = {8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.Debug|x64.ActiveCfg = Debug|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.Debug|x64.Build.0 = Debug|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.Release|x64.ActiveCfg = Release|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.Release|x64.Build.0 = Release|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C3829F0A-290C-39A6-83D8-3079E5B42D96}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{AF8F6CC8-25AD-3FDE-897F-6746090D4499}.Debug|x64.ActiveCfg = Debug|x64
		{AF8F6CC8-25AD-3FDE-897F-6746090D4499}.Release|x64.ActiveCfg = Release|x64
		{AF8F6CC8-25AD-3FDE-897F-6746090D4499}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AF8F6CC8-25AD-3FDE-897F-6746090D4499}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.Debug|x64.ActiveCfg = Debug|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.Debug|x64.Build.0 = Debug|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.Release|x64.ActiveCfg = Release|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.Release|x64.Build.0 = Release|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8F1B6A0E-258A-3F40-97FA-5B9F990CB1AE}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.Debug|x64.ActiveCfg = Debug|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.Debug|x64.Build.0 = Debug|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.Release|x64.ActiveCfg = Release|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.Release|x64.Build.0 = Release|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FEE8BE2B-75A8-329E-90BB-5EFF525F5EBC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.Debug|x64.ActiveCfg = Debug|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.Debug|x64.Build.0 = Debug|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.Release|x64.ActiveCfg = Release|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.Release|x64.Build.0 = Release|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BB5D482C-ADC4-3F61-9B77-ECC51595C9E7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F70B84E6-C146-343A-8E60-6660F71E67B8}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
